{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 8943067380521412502, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 8464492963398352750], [3150220818285335163, "url", false, 16630482765854159406], [3191507132440681679, "serde_untagged", false, 10815508900563369894], [4071963112282141418, "serde_with", false, 5956708945132994602], [4899080583175475170, "semver", false, 7470576787381197416], [5986029879202738730, "log", false, 8722473586600162715], [6606131838865521726, "ctor", false, 2946323150653884328], [7170110829644101142, "json_patch", false, 15117059404167297419], [8319709847752024821, "uuid", false, 11832009398776478], [8569119365930580996, "serde_json", false, 12223232392628892819], [9010263965687315507, "http", false, 4601769349494712840], [9451456094439810778, "regex", false, 14858649163905654004], [9556762810601084293, "brotli", false, 11444421916978605795], [9689903380558560274, "serde", false, 15288402749539603297], [10806645703491011684, "thiserror", false, 16067765827792213819], [11989259058781683633, "dunce", false, 1794205478168231105], [13625485746686963219, "anyhow", false, 3571148104063441989], [15609422047640926750, "toml", false, 9628122867934576211], [15622660310229662834, "walkdir", false, 5805374882390745969], [15932120279885307830, "memchr", false, 11261746297635178691], [17146114186171651583, "infer", false, 12603349095664120195], [17155886227862585100, "glob", false, 12242844930303994883], [17186037756130803222, "phf", false, 226820504110029407]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-dffc25596d9a65d4\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}