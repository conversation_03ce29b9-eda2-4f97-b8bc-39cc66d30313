{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "jdnotes", "version": "0.1.0", "identifier": "com.tauri.dev", "build": {"frontendDist": "../out", "devUrl": "http://localhost:3000", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "jdnotes", "width": 800, "height": 600, "resizable": true, "fullscreen": false, "decorations": false, "transparent": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}