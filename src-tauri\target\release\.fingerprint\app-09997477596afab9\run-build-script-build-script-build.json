{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 14577550233228660332], [12092653563678505622, "build_script_build", false, 17539451863137400926], [8324462083842905811, "build_script_build", false, 6999821001594789551]], "local": [{"RerunIfChanged": {"output": "release\\build\\app-09997477596afab9\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}