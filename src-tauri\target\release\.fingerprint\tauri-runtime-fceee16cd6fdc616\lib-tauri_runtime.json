{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 10389962791146233759, "deps": [[654232091421095663, "tauri_utils", false, 7936589195322987390], [3150220818285335163, "url", false, 16630482765854159406], [4143744114649553716, "raw_window_handle", false, 13056677237480622340], [7606335748176206944, "dpi", false, 9186513188024687967], [8569119365930580996, "serde_json", false, 12223232392628892819], [9010263965687315507, "http", false, 4601769349494712840], [9689903380558560274, "serde", false, 15288402749539603297], [10806645703491011684, "thiserror", false, 16067765827792213819], [12943761728066819757, "build_script_build", false, 12240091969442871126], [14585479307175734061, "windows", false, 1109905249475195623], [16727543399706004146, "cookie", false, 11684825410004884960]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-fceee16cd6fdc616\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}