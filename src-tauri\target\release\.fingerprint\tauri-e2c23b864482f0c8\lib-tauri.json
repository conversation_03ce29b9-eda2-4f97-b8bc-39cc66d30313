{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 14661550995293435084, "deps": [[40386456601120721, "percent_encoding", false, 7931579284272468289], [654232091421095663, "tauri_utils", false, 7936589195322987390], [1200537532907108615, "url<PERSON><PERSON>n", false, 8464492963398352750], [2013030631243296465, "webview2_com", false, 8411453171090293004], [3150220818285335163, "url", false, 16630482765854159406], [3331586631144870129, "getrandom", false, 10543716520641357618], [4143744114649553716, "raw_window_handle", false, 13056677237480622340], [4494683389616423722, "muda", false, 1619456530224621465], [4919829919303820331, "serialize_to_javascript", false, 11024704647564183126], [5986029879202738730, "log", false, 8722473586600162715], [8569119365930580996, "serde_json", false, 12223232392628892819], [9010263965687315507, "http", false, 4601769349494712840], [9689903380558560274, "serde", false, 15288402749539603297], [10229185211513642314, "mime", false, 7811686386244689245], [10806645703491011684, "thiserror", false, 16067765827792213819], [11989259058781683633, "dunce", false, 1794205478168231105], [12092653563678505622, "build_script_build", false, 17539451863137400926], [12304025191202589669, "tauri_runtime_wry", false, 2003503893109886864], [12393800526703971956, "tokio", false, 3742966531378023293], [12565293087094287914, "window_vibrancy", false, 10130863564521043387], [12943761728066819757, "tauri_runtime", false, 6022904628444585489], [12986574360607194341, "serde_repr", false, 6411554530033698719], [13077543566650298139, "heck", false, 14669962587389215398], [13405681745520956630, "tauri_macros", false, 12978192070227067808], [13625485746686963219, "anyhow", false, 3571148104063441989], [14585479307175734061, "windows", false, 1109905249475195623], [16928111194414003569, "dirs", false, 6034787275942488462], [17155886227862585100, "glob", false, 12242844930303994883]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-e2c23b864482f0c8\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}