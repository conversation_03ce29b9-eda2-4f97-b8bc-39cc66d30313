{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 9831540453462500854, "deps": [[376837177317575824, "softbuffer", false, 15854853872108664373], [654232091421095663, "tauri_utils", false, 7936589195322987390], [2013030631243296465, "webview2_com", false, 8411453171090293004], [3150220818285335163, "url", false, 16630482765854159406], [3722963349756955755, "once_cell", false, 4515980865749838026], [4143744114649553716, "raw_window_handle", false, 13056677237480622340], [5986029879202738730, "log", false, 8722473586600162715], [8826339825490770380, "tao", false, 17752984327070419221], [9010263965687315507, "http", false, 4601769349494712840], [9141053277961803901, "wry", false, 9180729179089815123], [12304025191202589669, "build_script_build", false, 3892786894844389636], [12943761728066819757, "tauri_runtime", false, 6022904628444585489], [14585479307175734061, "windows", false, 1109905249475195623]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-9d5efec84e69bc4d\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}