"use client"

import React, { useState, useEffect } from "react"
import { Minus, Square, X, Moon, Sun } from "lucide-react"
import { Button } from "@/components/ui/button"

interface TitleBarProps {
  title?: string
  theme?: "light" | "dark"
  onThemeToggle?: () => void
}

export function TitleBar({ title = "简单笔记", theme = "light", onThemeToggle }: TitleBarProps) {
  const [isTauri, setIsTauri] = useState(false)

  useEffect(() => {
    // 检测是否在 Tauri 环境中
    const checkTauriEnvironment = async () => {
      try {
        // 检查是否存在 __TAURI__ 全局变量
        if (typeof window !== 'undefined' && (window as any).__TAURI__) {
          setIsTauri(true)
        }
      } catch (error) {
        setIsTauri(false)
      }
    }

    checkTauriEnvironment()
  }, [])

  // 如果不是 Tauri 环境，不渲染标题栏
  if (!isTauri) {
    return null
  }
  const handleMinimize = async () => {
    const { getCurrentWindow } = await import("@tauri-apps/api/window")
    getCurrentWindow().minimize()
  }

  const handleMaximize = async () => {
    const { getCurrentWindow } = await import("@tauri-apps/api/window")
    const window = getCurrentWindow()
    const isMaximized = await window.isMaximized()
    if (isMaximized) {
      window.unmaximize()
    } else {
      window.maximize()
    }
  }

  const handleClose = async () => {
    const { getCurrentWindow } = await import("@tauri-apps/api/window")
    getCurrentWindow().close()
  }

  return (
    <div
      data-tauri-drag-region
      className="flex items-center justify-between h-10 bg-white dark:bg-background border-b border-gray-200 dark:border-border select-none shadow-sm"
    >
      {/* 左侧：应用标题 */}
      <div className="flex items-center px-4 h-full">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-sm flex items-center justify-center">
            <span className="text-white text-xs font-bold">📝</span>
          </div>
          <span className="text-sm font-medium text-gray-700 dark:text-foreground truncate">
            {title}
          </span>
        </div>
      </div>

      {/* 中间：可拖拽区域 */}
      <div className="flex-1 h-full" data-tauri-drag-region />

      {/* 右侧：控制按钮 */}
      <div className="flex items-center h-full">
        {/* 主题切换按钮 */}
        {onThemeToggle && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onThemeToggle}
            className="h-10 w-10 p-0 hover:bg-gray-100 dark:hover:bg-muted rounded-none transition-colors"
            title="切换主题"
          >
            {theme === "dark" ? (
              <Sun className="h-4 w-4 text-gray-600 dark:text-muted-foreground" />
            ) : (
              <Moon className="h-4 w-4 text-gray-600 dark:text-muted-foreground" />
            )}
          </Button>
        )}

        {/* 最小化按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleMinimize}
          className="h-10 w-12 p-0 hover:bg-gray-100 dark:hover:bg-muted rounded-none transition-colors"
          title="最小化"
        >
          <Minus className="h-4 w-4 text-gray-600 dark:text-muted-foreground" />
        </Button>

        {/* 最大化/还原按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleMaximize}
          className="h-10 w-12 p-0 hover:bg-gray-100 dark:hover:bg-muted rounded-none transition-colors"
          title="最大化/还原"
        >
          <Square className="h-4 w-4 text-gray-600 dark:text-muted-foreground" />
        </Button>

        {/* 关闭按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-10 w-12 p-0 hover:bg-red-500 hover:text-white rounded-none transition-colors group"
          title="关闭"
        >
          <X className="h-4 w-4 text-gray-600 dark:text-muted-foreground group-hover:text-white" />
        </Button>
      </div>
    </div>
  )
}
